"""
Test script for the Locator Resolver Testing Application.

This script demonstrates the functionality of the testing interface
by running some basic tests programmatically.

© 2025 Cogniron All Rights Reserved.
"""

import json
from locator_tester_constants import TestableFunction, SAMPLE_STEP_DATA, SAMPLE_ELEMENT_MATCHES
from locator_tester_helpers import generate_sample_data, validate_json_input, format_test_result, TestResult
from locator_tester_state import LocatorTesterState

# Import locator_resolver functions
from core.locator_resolver import (
    resolve_locator_conflicts, _detect_locator_conflicts, _calculate_locator_reliability,
    _evaluate_css_selector_reliability, _evaluate_xpath_reliability
)


def test_sample_data_generation():
    """Test sample data generation for different functions."""
    print("🧪 Testing Sample Data Generation")
    print("=" * 50)
    
    for function in TestableFunction:
        print(f"\n📋 Function: {function.value}")
        
        for scenario in ["basic", "complex", "edge_case"]:
            try:
                sample_data = generate_sample_data(function, scenario)
                print(f"  ✅ {scenario}: Generated {len(sample_data)} parameters")
            except Exception as e:
                print(f"  ❌ {scenario}: Error - {str(e)}")


def test_json_validation():
    """Test JSON validation functionality."""
    print("\n🔍 Testing JSON Validation")
    print("=" * 50)
    
    # Test valid JSON
    valid_json = json.dumps(SAMPLE_STEP_DATA["basic_css"])
    is_valid, result = validate_json_input(valid_json, dict)
    print(f"✅ Valid JSON: {is_valid}")
    
    # Test invalid JSON
    invalid_json = '{"invalid": json,}'
    is_valid, result = validate_json_input(invalid_json, dict)
    print(f"❌ Invalid JSON: {is_valid} - {result}")
    
    # Test wrong type
    list_json = json.dumps([1, 2, 3])
    is_valid, result = validate_json_input(list_json, dict)
    print(f"⚠️ Wrong type: {is_valid} - {result}")


def test_function_execution():
    """Test actual function execution."""
    print("\n⚡ Testing Function Execution")
    print("=" * 50)
    
    # Test resolve_locator_conflicts
    try:
        step_data = SAMPLE_STEP_DATA["basic_css"]
        element_matches = SAMPLE_ELEMENT_MATCHES["single_match"]
        
        result = resolve_locator_conflicts(
            step_data=step_data,
            element_matches=element_matches,
            step_no="1",
            test_case_id="TEST_001"
        )
        
        print("✅ resolve_locator_conflicts executed successfully")
        print(f"   Strategy: {result.get('resolved_locator_strategy', 'N/A')}")
        print(f"   Locator: {result.get('resolved_locator', 'N/A')}")
        print(f"   Confidence: {result.get('confidence_score', 'N/A')}")
        
    except Exception as e:
        print(f"❌ resolve_locator_conflicts failed: {str(e)}")
    
    # Test _detect_locator_conflicts
    try:
        conflicts = _detect_locator_conflicts(
            step_data=SAMPLE_STEP_DATA["basic_css"],
            element_matches=SAMPLE_ELEMENT_MATCHES["conflicting_matches"]
        )
        
        print(f"✅ _detect_locator_conflicts: {conflicts}")
        
    except Exception as e:
        print(f"❌ _detect_locator_conflicts failed: {str(e)}")
    
    # Test _calculate_locator_reliability
    try:
        element = {
            "selector": "#login-button",
            "xpath": "//button[@id='login-button']",
            "attributes": {"id": "login-button", "type": "button"}
        }
        
        reliability = _calculate_locator_reliability(element)
        print(f"✅ _calculate_locator_reliability: {reliability}")
        
    except Exception as e:
        print(f"❌ _calculate_locator_reliability failed: {str(e)}")
    
    # Test CSS selector evaluation
    try:
        css_score = _evaluate_css_selector_reliability("#unique-id")
        print(f"✅ _evaluate_css_selector_reliability: {css_score}")
        
    except Exception as e:
        print(f"❌ _evaluate_css_selector_reliability failed: {str(e)}")
    
    # Test XPath evaluation
    try:
        xpath_score = _evaluate_xpath_reliability("//input[@id='username']")
        print(f"✅ _evaluate_xpath_reliability: {xpath_score}")
        
    except Exception as e:
        print(f"❌ _evaluate_xpath_reliability failed: {str(e)}")


def test_state_manager():
    """Test the state manager functionality."""
    print("\n🏗️ Testing State Manager")
    print("=" * 50)
    
    # Create state instance
    state = LocatorTesterState()
    print("✅ State manager created")
    
    # Test function selection
    state.update_function_selection(TestableFunction.RESOLVE_CONFLICTS)
    print(f"✅ Function selected: {state.selected_function.value}")
    
    # Test scenario loading
    success = state.load_test_scenario("basic_resolution")
    print(f"✅ Scenario loaded: {success}")
    
    # Test result storage
    test_result = TestResult(
        success=True,
        result={"test": "data"},
        execution_time=0.123,
        function_name="test_function"
    )
    
    state.update_test_result(test_result)
    print(f"✅ Result stored: {len(state.test_history)} items in history")
    
    # Test validation
    state.update_validation_error("test_field", "test error")
    has_errors = state.has_validation_errors()
    print(f"✅ Validation error handling: {has_errors}")
    
    state._clear_validation_errors()
    has_errors = state.has_validation_errors()
    print(f"✅ Validation error clearing: {not has_errors}")


def test_result_formatting():
    """Test result formatting functionality."""
    print("\n📊 Testing Result Formatting")
    print("=" * 50)
    
    # Test successful result
    success_result = TestResult(
        success=True,
        result={
            "resolved_locator_strategy": "id",
            "resolved_locator": "login-button",
            "confidence_score": 0.95
        },
        execution_time=0.045,
        function_name="resolve_locator_conflicts"
    )
    
    formatted = format_test_result(success_result)
    print("✅ Success result formatted:")
    print(f"   Status: {formatted['status']}")
    print(f"   Summary: {formatted.get('result_summary', 'N/A')}")
    
    # Test error result
    error_result = TestResult(
        success=False,
        result=None,
        execution_time=0.012,
        error_message="Test error message",
        error_type="TestError",
        function_name="test_function"
    )
    
    formatted = format_test_result(error_result)
    print("✅ Error result formatted:")
    print(f"   Status: {formatted['status']}")
    print(f"   Error: {formatted['error']['message']}")


def main():
    """Run all tests."""
    print("🔍 Locator Resolver Testing Application - Test Suite")
    print("=" * 60)
    
    try:
        test_sample_data_generation()
        test_json_validation()
        test_function_execution()
        test_state_manager()
        test_result_formatting()
        
        print("\n🎉 All tests completed successfully!")
        print("=" * 60)
        print("The Locator Resolver Testing Application is ready to use.")
        print("Run: streamlit run locator_resolver_tester.py")
        
    except Exception as e:
        print(f"\n💥 Test suite failed: {str(e)}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
