"""
State Manager for the Locator Resolver Testing Application.

This module provides a centralized state management class for the testing interface.
It follows the same patterns as the main ScriptWeaver StateManager but is tailored
for the testing application's specific needs.

© 2025 Cogniron All Rights Reserved.
"""

from dataclasses import dataclass, field
from typing import Dict, List, Any, Optional
import streamlit as st

from debug_utils import debug
from locator_tester_constants import TestableFunction, TEST_SCENARIOS
from locator_tester_helpers import TestResult


@dataclass
class LocatorTesterState:
    """
    Centralized state manager for the Locator Resolver Testing Application.
    
    This class stores all application state in a structured way, organized by logical categories.
    It uses <PERSON>'s dataclass for clean definition and type hints for better IDE support.
    
    The state is organized into these categories:
    1. Function selection and configuration
    2. Input parameters and test data
    3. Test execution results and history
    4. UI state and preferences
    """
    
    # ───── Function Selection ─────
    selected_function: TestableFunction = TestableFunction.RESOLVE_CONFLICTS
    function_description: str = ""
    
    # ───── Input Parameters ─────
    step_data_json: str = ""
    element_matches_json: str = ""
    step_no: str = "1"
    test_case_id: str = "TC_001"
    element_json: str = ""
    selector: str = ""
    xpath: str = ""
    element_match_json: str = ""
    reason: str = ""
    confidence: float = 0.8
    
    # ───── Test Execution ─────
    current_result: Optional[TestResult] = None
    test_history: List[TestResult] = field(default_factory=list)
    execution_in_progress: bool = False
    
    # ───── UI State ─────
    show_advanced_options: bool = False
    show_test_history: bool = False
    show_sample_data: bool = True
    selected_scenario: str = "basic_resolution"
    auto_format_json: bool = True
    
    # ───── Validation State ─────
    step_data_valid: bool = True
    element_matches_valid: bool = True
    element_valid: bool = True
    element_match_valid: bool = True
    validation_errors: Dict[str, str] = field(default_factory=dict)
    
    def __post_init__(self):
        """Post-initialization to set up default values."""
        if not self.function_description:
            self.function_description = self._get_function_description()
    
    def init_in_session(self, st_module):
        """
        Initialize the state manager in the Streamlit session state.
        
        Args:
            st_module: The Streamlit module instance
        """
        if "locator_tester_state" not in st_module.session_state:
            st_module.session_state["locator_tester_state"] = self
            debug("Initialized locator tester state in session",
                  stage="locator_tester", operation="state_initialization")
        else:
            # Ensure existing state has all required fields
            existing_state = st_module.session_state["locator_tester_state"]
            self._upgrade_existing_state(existing_state)
    
    @staticmethod
    def get(st_module) -> 'LocatorTesterState':
        """
        Get the state manager instance from Streamlit session state.
        
        Args:
            st_module: The Streamlit module instance
            
        Returns:
            LocatorTesterState instance
        """
        if "locator_tester_state" not in st_module.session_state:
            # Create new instance if it doesn't exist
            state = LocatorTesterState()
            state.init_in_session(st_module)
            return state
        
        return st_module.session_state["locator_tester_state"]
    
    def update_function_selection(self, function: TestableFunction) -> None:
        """
        Update the selected function and related state.
        
        Args:
            function: The newly selected function
        """
        if self.selected_function != function:
            debug(f"Function selection changed from {self.selected_function.value} to {function.value}",
                  stage="locator_tester", operation="function_selection",
                  context={"old_function": self.selected_function.value, "new_function": function.value})
            
            self.selected_function = function
            self.function_description = self._get_function_description()
            self._clear_validation_errors()
            self._reset_input_parameters()
    
    def update_test_result(self, result: TestResult) -> None:
        """
        Update the current test result and add to history.
        
        Args:
            result: The test result to store
        """
        self.current_result = result
        self.test_history.append(result)
        self.execution_in_progress = False
        
        debug(f"Test result updated for {result.function_name}",
              stage="locator_tester", operation="result_update",
              context={"function": result.function_name, "success": result.success,
                      "execution_time": result.execution_time})
    
    def clear_test_history(self) -> None:
        """Clear all test history."""
        self.test_history.clear()
        self.current_result = None
        debug("Test history cleared", stage="locator_tester", operation="history_clear")
    
    def set_execution_in_progress(self, in_progress: bool) -> None:
        """
        Set the execution in progress flag.
        
        Args:
            in_progress: Whether execution is in progress
        """
        self.execution_in_progress = in_progress
        if in_progress:
            debug("Test execution started", stage="locator_tester", operation="execution_start")
    
    def update_validation_error(self, field: str, error: str) -> None:
        """
        Update validation error for a specific field.
        
        Args:
            field: Field name
            error: Error message
        """
        if error:
            self.validation_errors[field] = error
        else:
            self.validation_errors.pop(field, None)
    
    def has_validation_errors(self) -> bool:
        """Check if there are any validation errors."""
        return bool(self.validation_errors)
    
    def get_validation_error(self, field: str) -> Optional[str]:
        """Get validation error for a specific field."""
        return self.validation_errors.get(field)
    
    def load_test_scenario(self, scenario_name: str) -> bool:
        """
        Load a predefined test scenario.
        
        Args:
            scenario_name: Name of the scenario to load
            
        Returns:
            True if scenario was loaded successfully
        """
        try:
            scenario = TEST_SCENARIOS.get(scenario_name)
            if not scenario:
                debug(f"Test scenario not found: {scenario_name}",
                      stage="locator_tester", operation="scenario_load_error",
                      context={"scenario_name": scenario_name})
                return False
            
            # Update state with scenario data
            self.selected_scenario = scenario_name
            
            if "step_data" in scenario:
                import json
                self.step_data_json = json.dumps(scenario["step_data"], indent=2)
            
            if "element_matches" in scenario:
                import json
                self.element_matches_json = json.dumps(scenario["element_matches"], indent=2)
            
            if "step_no" in scenario:
                self.step_no = scenario["step_no"]
            
            if "test_case_id" in scenario:
                self.test_case_id = scenario["test_case_id"]
            
            self._clear_validation_errors()
            
            debug(f"Loaded test scenario: {scenario_name}",
                  stage="locator_tester", operation="scenario_load",
                  context={"scenario_name": scenario_name})
            
            return True
            
        except Exception as e:
            debug(f"Error loading test scenario: {str(e)}",
                  stage="locator_tester", operation="scenario_load_error",
                  context={"scenario_name": scenario_name, "error": str(e)})
            return False
    
    def reset_all_state(self) -> None:
        """Reset all state to default values."""
        self.selected_function = TestableFunction.RESOLVE_CONFLICTS
        self.function_description = self._get_function_description()
        self._reset_input_parameters()
        self.current_result = None
        self.test_history.clear()
        self.execution_in_progress = False
        self._clear_validation_errors()
        
        debug("All state reset to defaults", stage="locator_tester", operation="state_reset")
    
    def _get_function_description(self) -> str:
        """Get description for the currently selected function."""
        from locator_tester_constants import FUNCTION_DESCRIPTIONS
        return FUNCTION_DESCRIPTIONS.get(self.selected_function, {}).get("description", "")
    
    def _reset_input_parameters(self) -> None:
        """Reset input parameters to default values."""
        self.step_data_json = ""
        self.element_matches_json = ""
        self.step_no = "1"
        self.test_case_id = "TC_001"
        self.element_json = ""
        self.selector = ""
        self.xpath = ""
        self.element_match_json = ""
        self.reason = ""
        self.confidence = 0.8
    
    def _clear_validation_errors(self) -> None:
        """Clear all validation errors."""
        self.validation_errors.clear()
        self.step_data_valid = True
        self.element_matches_valid = True
        self.element_valid = True
        self.element_match_valid = True
    
    def _upgrade_existing_state(self, existing_state: 'LocatorTesterState') -> None:
        """
        Upgrade existing state to ensure it has all required fields.
        
        Args:
            existing_state: The existing state instance
        """
        # Add any missing fields with default values
        if not hasattr(existing_state, 'auto_format_json'):
            existing_state.auto_format_json = True
        
        if not hasattr(existing_state, 'validation_errors'):
            existing_state.validation_errors = {}
        
        # Add other missing fields as needed
        debug("Upgraded existing locator tester state",
              stage="locator_tester", operation="state_upgrade")
