"""
Pure helper functions for the Locator Resolver Testing Application.

This module contains stateless utility functions for test data generation,
validation, and result formatting that don't depend on Streamlit's session state.

© 2025 Cogniron All Rights Reserved.
"""

import json
import time
import traceback
from typing import Dict, List, Any, Optional, Tuple, Union
from dataclasses import dataclass
from datetime import datetime

from debug_utils import debug
from locator_tester_constants import (
    TestableFunction, FUNCTION_DESCRIPTIONS, SAMPLE_STEP_DATA,
    SAMPLE_ELEMENT_MATCHES, SAMPLE_CSS_SELECTORS, SAMPLE_XPATH_EXPRESSIONS,
    SAMPLE_ELEMENTS, TEST_SCENARIOS
)


@dataclass
class TestResult:
    """Container for test execution results."""
    success: bool
    result: Any
    execution_time: float
    error_message: Optional[str] = None
    error_type: Optional[str] = None
    function_name: str = ""


def validate_json_input(json_str: str, expected_type: type = dict) -> <PERSON><PERSON>[bool, Union[Dict, List, str]]:
    """
    Validate and parse <PERSON><PERSON><PERSON> input string.
    
    Args:
        json_str: JSON string to validate
        expected_type: Expected type (dict or list)
        
    Returns:
        Tuple of (is_valid, parsed_data_or_error_message)
    """
    try:
        if not json_str.strip():
            return False, "JSON input cannot be empty"
            
        parsed = json.loads(json_str)
        
        if not isinstance(parsed, expected_type):
            return False, f"Expected {expected_type.__name__}, got {type(parsed).__name__}"
            
        return True, parsed
        
    except json.JSONDecodeError as e:
        return False, f"Invalid JSON: {str(e)}"
    except Exception as e:
        return False, f"Validation error: {str(e)}"


def format_test_result(result: TestResult) -> Dict[str, Any]:
    """
    Format test result for display in the UI.
    
    Args:
        result: TestResult object
        
    Returns:
        Formatted result dictionary
    """
    try:
        formatted = {
            "status": "✅ Success" if result.success else "❌ Failed",
            "execution_time": f"{result.execution_time:.4f}s",
            "function": result.function_name,
            "timestamp": datetime.now().strftime("%H:%M:%S")
        }
        
        if result.success:
            formatted["result"] = result.result
            formatted["result_type"] = type(result.result).__name__
            
            # Add specific formatting for different result types
            if isinstance(result.result, dict):
                formatted["result_summary"] = _summarize_dict_result(result.result)
            elif isinstance(result.result, (int, float)):
                formatted["result_summary"] = f"Value: {result.result}"
            elif isinstance(result.result, bool):
                formatted["result_summary"] = f"Boolean: {result.result}"
            else:
                formatted["result_summary"] = f"Type: {type(result.result).__name__}"
                
        else:
            formatted["error"] = {
                "type": result.error_type or "Unknown",
                "message": result.error_message or "No error message available"
            }
            
        return formatted
        
    except Exception as e:
        debug(f"Error formatting test result: {str(e)}", 
              stage="locator_tester", operation="result_formatting")
        return {
            "status": "⚠️ Format Error",
            "error": {"type": "FormattingError", "message": str(e)},
            "execution_time": "N/A",
            "function": result.function_name,
            "timestamp": datetime.now().strftime("%H:%M:%S")
        }


def _summarize_dict_result(result_dict: Dict[str, Any]) -> str:
    """Create a summary of dictionary results for quick viewing."""
    try:
        if not result_dict:
            return "Empty dictionary"
            
        # Special handling for locator resolution results
        if "resolved_locator_strategy" in result_dict:
            strategy = result_dict.get("resolved_locator_strategy", "unknown")
            locator = result_dict.get("resolved_locator", "unknown")
            confidence = result_dict.get("confidence_score", 0)
            return f"Strategy: {strategy}, Locator: {locator}, Confidence: {confidence:.2f}"
            
        # General dictionary summary
        key_count = len(result_dict)
        sample_keys = list(result_dict.keys())[:3]
        if key_count > 3:
            sample_keys.append("...")
            
        return f"{key_count} keys: {', '.join(str(k) for k in sample_keys)}"
        
    except Exception:
        return "Dictionary result (summary unavailable)"


def generate_sample_data(function: TestableFunction, scenario: str = "basic") -> Dict[str, Any]:
    """
    Generate sample input data for testing a specific function.
    
    Args:
        function: Function to generate data for
        scenario: Scenario type (basic, complex, edge_case)
        
    Returns:
        Dictionary with sample parameters
    """
    try:
        debug(f"Generating sample data for {function.value} with scenario {scenario}",
              stage="locator_tester", operation="sample_data_generation",
              context={"function": function.value, "scenario": scenario})
        
        if function == TestableFunction.RESOLVE_CONFLICTS:
            return _generate_resolve_conflicts_data(scenario)
        elif function == TestableFunction.DETECT_CONFLICTS:
            return _generate_detect_conflicts_data(scenario)
        elif function == TestableFunction.APPLY_HEURISTICS:
            return _generate_apply_heuristics_data(scenario)
        elif function == TestableFunction.CALCULATE_RELIABILITY:
            return _generate_calculate_reliability_data(scenario)
        elif function == TestableFunction.EVALUATE_CSS:
            return _generate_evaluate_css_data(scenario)
        elif function == TestableFunction.EVALUATE_XPATH:
            return _generate_evaluate_xpath_data(scenario)
        elif function == TestableFunction.CREATE_RESULT:
            return _generate_create_result_data(scenario)
        else:
            return {}
            
    except Exception as e:
        debug(f"Error generating sample data: {str(e)}",
              stage="locator_tester", operation="sample_data_generation_error",
              context={"function": function.value, "scenario": scenario, "error": str(e)})
        return {}


def _generate_resolve_conflicts_data(scenario: str) -> Dict[str, Any]:
    """Generate sample data for resolve_locator_conflicts function."""
    if scenario == "complex":
        return {
            "step_data": SAMPLE_STEP_DATA["basic_css"],
            "element_matches": SAMPLE_ELEMENT_MATCHES["multiple_matches"],
            "step_no": "2",
            "test_case_id": "TC_COMPLEX_001"
        }
    elif scenario == "edge_case":
        return {
            "step_data": SAMPLE_STEP_DATA["empty_locator"],
            "element_matches": SAMPLE_ELEMENT_MATCHES["empty_matches"],
            "step_no": "1",
            "test_case_id": "TC_EDGE_001"
        }
    else:  # basic
        return {
            "step_data": SAMPLE_STEP_DATA["basic_css"],
            "element_matches": SAMPLE_ELEMENT_MATCHES["single_match"],
            "step_no": "1",
            "test_case_id": "TC_BASIC_001"
        }


def _generate_detect_conflicts_data(scenario: str) -> Dict[str, Any]:
    """Generate sample data for _detect_locator_conflicts function."""
    if scenario == "complex":
        return {
            "step_data": SAMPLE_STEP_DATA["basic_xpath"],
            "element_matches": SAMPLE_ELEMENT_MATCHES["conflicting_matches"]
        }
    elif scenario == "edge_case":
        return {
            "step_data": SAMPLE_STEP_DATA["navigation"],
            "element_matches": SAMPLE_ELEMENT_MATCHES["empty_matches"]
        }
    else:  # basic
        return {
            "step_data": SAMPLE_STEP_DATA["basic_css"],
            "element_matches": SAMPLE_ELEMENT_MATCHES["single_match"]
        }


def _generate_apply_heuristics_data(scenario: str) -> Dict[str, Any]:
    """Generate sample data for _apply_resolution_heuristics function."""
    if scenario == "complex":
        return {
            "step_data": SAMPLE_STEP_DATA["id_locator"],
            "element_matches": SAMPLE_ELEMENT_MATCHES["multiple_matches"],
            "step_no": "3",
            "test_case_id": "TC_HEURISTICS_001"
        }
    elif scenario == "edge_case":
        return {
            "step_data": SAMPLE_STEP_DATA["empty_locator"],
            "element_matches": SAMPLE_ELEMENT_MATCHES["conflicting_matches"],
            "step_no": "1",
            "test_case_id": "TC_EDGE_HEURISTICS"
        }
    else:  # basic
        return {
            "step_data": SAMPLE_STEP_DATA["basic_css"],
            "element_matches": SAMPLE_ELEMENT_MATCHES["single_match"],
            "step_no": "1",
            "test_case_id": "TC_BASIC_HEURISTICS"
        }


def _generate_calculate_reliability_data(scenario: str) -> Dict[str, Any]:
    """Generate sample data for _calculate_locator_reliability function."""
    if scenario == "complex":
        return {"element": SAMPLE_ELEMENTS[0]}  # Element with ID
    elif scenario == "edge_case":
        return {"element": SAMPLE_ELEMENTS[4]}  # Empty element
    else:  # basic
        return {"element": SAMPLE_ELEMENTS[1]}  # Element with name attribute


def _generate_evaluate_css_data(scenario: str) -> Dict[str, Any]:
    """Generate sample data for _evaluate_css_selector_reliability function."""
    if scenario == "complex":
        return {"selector": SAMPLE_CSS_SELECTORS[6]}  # Complex selector
    elif scenario == "edge_case":
        return {"selector": SAMPLE_CSS_SELECTORS[9]}  # Empty selector
    else:  # basic
        return {"selector": SAMPLE_CSS_SELECTORS[0]}  # ID selector


def _generate_evaluate_xpath_data(scenario: str) -> Dict[str, Any]:
    """Generate sample data for _evaluate_xpath_reliability function."""
    if scenario == "complex":
        return {"xpath": SAMPLE_XPATH_EXPRESSIONS[6]}  # Position-based XPath
    elif scenario == "edge_case":
        return {"xpath": SAMPLE_XPATH_EXPRESSIONS[9]}  # Empty XPath
    else:  # basic
        return {"xpath": SAMPLE_XPATH_EXPRESSIONS[0]}  # ID-based XPath


def _generate_create_result_data(scenario: str) -> Dict[str, Any]:
    """Generate sample data for _create_resolution_result function."""
    if scenario == "complex":
        return {
            "element_match": {
                "element": SAMPLE_ELEMENTS[0],
                "score": 0.95,
                "manually_selected": True
            },
            "reason": "Complex test scenario with high confidence",
            "confidence": 0.95
        }
    elif scenario == "edge_case":
        return {
            "element_match": {
                "element": SAMPLE_ELEMENTS[4],
                "score": 0.0,
                "manually_selected": False
            },
            "reason": "Edge case with empty element",
            "confidence": 0.0
        }
    else:  # basic
        return {
            "element_match": {
                "element": SAMPLE_ELEMENTS[1],
                "score": 0.85,
                "manually_selected": False
            },
            "reason": "Basic test scenario",
            "confidence": 0.85
        }


def get_test_scenario_data(scenario_name: str) -> Optional[Dict[str, Any]]:
    """
    Get predefined test scenario data.
    
    Args:
        scenario_name: Name of the test scenario
        
    Returns:
        Test scenario data or None if not found
    """
    try:
        return TEST_SCENARIOS.get(scenario_name)
    except Exception as e:
        debug(f"Error getting test scenario data: {str(e)}",
              stage="locator_tester", operation="scenario_data_retrieval",
              context={"scenario_name": scenario_name, "error": str(e)})
        return None


def validate_function_parameters(function: TestableFunction, parameters: Dict[str, Any]) -> Tuple[bool, str]:
    """
    Validate that all required parameters are provided for a function.
    
    Args:
        function: Function to validate parameters for
        parameters: Dictionary of parameter values
        
    Returns:
        Tuple of (is_valid, error_message)
    """
    try:
        required_params = FUNCTION_DESCRIPTIONS[function]["parameters"]
        
        for param in required_params:
            if param not in parameters:
                return False, f"Missing required parameter: {param}"
            
            value = parameters[param]
            if value is None or (isinstance(value, str) and not value.strip()):
                return False, f"Parameter '{param}' cannot be empty"
                
        return True, ""
        
    except Exception as e:
        return False, f"Validation error: {str(e)}"
