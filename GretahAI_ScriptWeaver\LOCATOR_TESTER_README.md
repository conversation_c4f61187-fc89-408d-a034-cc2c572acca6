# Locator Resolver Testing Interface

## Overview

The Locator Resolver Testing Interface is a standalone Streamlit application designed for testing and debugging functions in the `locator_resolver` module. It provides an interactive interface for validating behavior, troubleshooting issues, and testing different scenarios.

## Features

### 🔧 Function Testing
- Test all major `locator_resolver` functions:
  - `resolve_locator_conflicts()` - Main conflict resolution function
  - `_detect_locator_conflicts()` - Conflict detection
  - `_apply_resolution_heuristics()` - Heuristic application
  - `_calculate_locator_reliability()` - Reliability calculation
  - `_evaluate_css_selector_reliability()` - CSS selector evaluation
  - `_evaluate_xpath_reliability()` - XPath evaluation
  - `_create_resolution_result()` - Result creation

### 📋 Test Scenarios
- **Predefined Scenarios**: Load common test cases with pre-configured data
- **Sample Data Generation**: Generate basic, complex, and edge case test data
- **Custom Input**: Create your own test scenarios with JSON editors

### 📊 Results & Analysis
- **Visual Indicators**: Success (✅), failure (❌), and warning (⚠️) status
- **Execution Metrics**: Timing information and performance data
- **Detailed Output**: Full result display with JSON formatting
- **Error Handling**: Comprehensive error reporting with stack traces

### 📜 Test History
- **Execution Tracking**: Keep history of all test runs
- **Statistics**: Success/failure rates and performance metrics
- **Quick Review**: Summary table of recent test executions

## Usage

### Starting the Application

```bash
cd GretahAI_ScriptWeaver
streamlit run locator_resolver_tester.py
```

### Basic Workflow

1. **Select Function**: Choose which `locator_resolver` function to test from the sidebar
2. **Load Test Data**: Use predefined scenarios or generate sample data
3. **Configure Parameters**: Adjust input parameters using the JSON editors
4. **Execute Test**: Run the function and view results
5. **Analyze Results**: Review output, timing, and any errors

### Test Scenarios

#### Basic Resolution
Tests basic conflict resolution between step data and element matches.

#### Manual Selection Priority
Validates that manually selected elements receive highest priority.

#### Conflicting Strategies
Tests resolution when step data conflicts with element matches.

#### Navigation Step
Tests handling of navigation steps with URL locators.

#### Empty Data Handling
Tests behavior with empty or invalid input data.

## Interface Components

### Sidebar
- **Function Selection**: Dropdown to choose test function
- **Function Description**: Details about selected function
- **Test Scenarios**: Load predefined test cases
- **Sample Data**: Generate test data for different scenarios
- **Controls**: Clear data, reset parameters, toggle options

### Main Content
- **Parameter Input**: JSON editors for function parameters
- **Test Execution**: Execute button and validation controls
- **Results Display**: Test output with formatting and metrics
- **Test History**: Historical test execution data

## Technical Details

### Architecture
- **StateManager Pattern**: Centralized state management with `LocatorTesterState`
- **GRETAH Logging**: Structured logging using `debug()` calls
- **Modular Design**: Separated constants, helpers, and state management
- **Professional UI**: Clean typography and enterprise styling

### Error Handling
- **JSON Validation**: Real-time validation of JSON input
- **Parameter Validation**: Check required parameters before execution
- **Exception Handling**: Graceful error handling with user-friendly messages
- **Logging**: Comprehensive debug logging for troubleshooting

### Performance
- **Execution Timing**: Measure function execution time
- **Caching**: Use `st.cache_data` for expensive operations
- **Validation**: Immediate feedback on input validation

## File Structure

```
GretahAI_ScriptWeaver/
├── locator_resolver_tester.py      # Main Streamlit application
├── locator_tester_state.py         # StateManager for testing app
├── locator_tester_helpers.py       # Pure helper functions
├── locator_tester_constants.py     # Constants and sample data
└── LOCATOR_TESTER_README.md        # This documentation
```

## Sample Data

The application includes comprehensive sample data for testing:

### Step Data Examples
- Basic CSS locators
- XPath expressions
- ID-based locators
- Navigation URLs
- Empty/invalid data

### Element Matches Examples
- Single element matches
- Multiple competing matches
- Conflicting strategies
- Empty match lists

### CSS Selectors
- ID selectors (`#unique-id`)
- Class selectors (`.class-name`)
- Attribute selectors (`[data-testid='button']`)
- Complex selectors (`div > p.content`)

### XPath Expressions
- ID-based XPath (`//input[@id='username']`)
- Text-based XPath (`//button[text()='Login']`)
- Position-based XPath (`//input[@type='password'][1]`)
- Complex XPath (`//div[contains(@class, 'form')]`)

## Troubleshooting

### Common Issues

1. **JSON Validation Errors**
   - Ensure JSON syntax is correct
   - Use the auto-format option to fix formatting
   - Check for missing quotes or brackets

2. **Parameter Validation Failures**
   - Verify all required parameters are provided
   - Check parameter types match expectations
   - Use sample data generation for valid examples

3. **Function Execution Errors**
   - Review error messages in the results section
   - Check the test history for patterns
   - Enable debug logging for detailed information

### Debug Logging

The application uses GRETAH logging compliance with structured debug calls:

```python
debug("Test execution started", 
      stage="locator_tester", 
      operation="test_execution",
      context={"function": "resolve_locator_conflicts"})
```

Enable debug logging by setting environment variables:
```bash
export SCRIPTWEAVER_DEBUG=true
export GRETAH_LOG_CONSOLE=true
```

## Contributing

When adding new test scenarios or functions:

1. Update `locator_tester_constants.py` with new sample data
2. Add helper functions to `locator_tester_helpers.py`
3. Update the function mapping in the main application
4. Add appropriate logging and error handling

## License

© 2025 Cogniron All Rights Reserved.

This testing interface is part of the GRETAH-CaseForge project and follows the same licensing terms.
