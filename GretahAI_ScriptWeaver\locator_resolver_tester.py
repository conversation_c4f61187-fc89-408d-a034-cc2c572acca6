"""
Locator Resolver Testing Application

Interactive Streamlit application for testing and debugging functions in the
locator_resolver module. Provides a comprehensive interface for validating
behavior, troubleshooting issues, and testing different scenarios.

Usage:
    streamlit run locator_resolver_tester.py

© 2025 Cogniron All Rights Reserved.
"""

import streamlit as st
import json
import time
import traceback
from typing import Dict, Any, Optional

# Import application modules
from debug_utils import debug
from locator_tester_state import LocatorTesterState
from locator_tester_constants import (
    APP_TITLE, APP_DESCRIPTION, TestableFunction, FUNCTION_DESCRIPTIONS,
    TEST_SCENARIOS, UI_CONFIG
)
from locator_tester_helpers import (
    TestResult, validate_json_input, format_test_result, generate_sample_data,
    get_test_scenario_data, validate_function_parameters
)

# Import locator_resolver functions for testing
from core.locator_resolver import (
    resolve_locator_conflicts, _detect_locator_conflicts, _apply_resolution_heuristics,
    _calculate_locator_reliability, _evaluate_css_selector_reliability,
    _evaluate_xpath_reliability, _create_resolution_result
)


def main():
    """Main application entry point."""
    try:
        # Configure page
        st.set_page_config(
            page_title="Locator Resolver Tester",
            page_icon="🔍",
            layout="wide",
            initial_sidebar_state="expanded"
        )
        
        # Initialize state
        state = LocatorTesterState()
        state.init_in_session(st)
        state = LocatorTesterState.get(st)
        
        debug("Locator resolver tester application started",
              stage="locator_tester", operation="app_startup")
        
        # Apply custom CSS
        _apply_custom_css()
        
        # Render application
        _render_header()
        _render_sidebar(state)
        _render_main_content(state)
        
    except Exception as e:
        st.error(f"Application error: {str(e)}")
        debug(f"Application error: {str(e)}",
              stage="locator_tester", operation="app_error",
              context={"error": str(e), "traceback": traceback.format_exc()})


def _apply_custom_css():
    """Apply custom CSS for professional styling."""
    st.markdown("""
    <style>
    .main-header {
        background: linear-gradient(90deg, #1f4e79 0%, #2d5aa0 100%);
        color: white;
        padding: 1rem;
        border-radius: 0.5rem;
        margin-bottom: 2rem;
        text-align: center;
    }
    
    .function-card {
        background: #f8f9fa;
        border: 1px solid #dee2e6;
        border-radius: 0.5rem;
        padding: 1rem;
        margin-bottom: 1rem;
    }
    
    .result-success {
        background: #d4edda;
        border: 1px solid #c3e6cb;
        color: #155724;
        padding: 1rem;
        border-radius: 0.5rem;
        margin: 1rem 0;
    }
    
    .result-error {
        background: #f8d7da;
        border: 1px solid #f5c6cb;
        color: #721c24;
        padding: 1rem;
        border-radius: 0.5rem;
        margin: 1rem 0;
    }
    
    .result-warning {
        background: #fff3cd;
        border: 1px solid #ffeaa7;
        color: #856404;
        padding: 1rem;
        border-radius: 0.5rem;
        margin: 1rem 0;
    }
    
    .parameter-section {
        background: white;
        border: 1px solid #e9ecef;
        border-radius: 0.5rem;
        padding: 1.5rem;
        margin-bottom: 1rem;
    }
    
    .json-editor {
        font-family: 'Courier New', monospace;
        font-size: 0.9rem;
    }
    </style>
    """, unsafe_allow_html=True)


def _render_header():
    """Render the application header."""
    st.markdown(f"""
    <div class="main-header">
        <h1>{APP_TITLE}</h1>
        <p>{APP_DESCRIPTION}</p>
    </div>
    """, unsafe_allow_html=True)


def _render_sidebar(state: LocatorTesterState):
    """Render the sidebar with function selection and controls."""
    with st.sidebar:
        st.header("🔧 Function Selection")
        
        # Function selection
        function_options = {func.value: func for func in TestableFunction}
        selected_function_name = st.selectbox(
            "Select Function to Test",
            options=list(function_options.keys()),
            index=list(function_options.keys()).index(state.selected_function.value),
            help="Choose which locator_resolver function to test"
        )
        
        selected_function = function_options[selected_function_name]
        if selected_function != state.selected_function:
            state.update_function_selection(selected_function)
            st.rerun()
        
        # Function description
        func_info = FUNCTION_DESCRIPTIONS[state.selected_function]
        st.markdown(f"""
        <div class="function-card">
            <h4>{func_info['name']}</h4>
            <p>{func_info['description']}</p>
            <p><strong>Parameters:</strong> {', '.join(func_info['parameters'])}</p>
            <p><strong>Returns:</strong> {func_info['returns']}</p>
        </div>
        """, unsafe_allow_html=True)
        
        st.divider()
        
        # Test scenarios
        st.header("📋 Test Scenarios")
        scenario_options = list(TEST_SCENARIOS.keys())
        selected_scenario = st.selectbox(
            "Load Predefined Scenario",
            options=[""] + scenario_options,
            help="Load predefined test data for common scenarios"
        )
        
        if selected_scenario and selected_scenario != state.selected_scenario:
            if state.load_test_scenario(selected_scenario):
                st.success(f"Loaded scenario: {selected_scenario}")
                st.rerun()
            else:
                st.error(f"Failed to load scenario: {selected_scenario}")
        
        # Sample data generation
        st.subheader("🎲 Generate Sample Data")
        col1, col2 = st.columns(2)
        
        with col1:
            if st.button("Basic", help="Generate basic test data"):
                _load_sample_data(state, "basic")
                st.rerun()
        
        with col2:
            if st.button("Complex", help="Generate complex test data"):
                _load_sample_data(state, "complex")
                st.rerun()
        
        if st.button("Edge Case", help="Generate edge case test data"):
            _load_sample_data(state, "edge_case")
            st.rerun()
        
        st.divider()
        
        # Controls
        st.header("⚙️ Controls")
        
        if st.button("🗑️ Clear All", help="Clear all input data and results"):
            state.reset_all_state()
            st.rerun()
        
        if st.button("📜 Clear History", help="Clear test execution history"):
            state.clear_test_history()
            st.rerun()
        
        # UI Options
        st.subheader("🎛️ Options")
        state.show_advanced_options = st.checkbox(
            "Show Advanced Options",
            value=state.show_advanced_options
        )
        
        state.show_test_history = st.checkbox(
            "Show Test History",
            value=state.show_test_history
        )
        
        state.auto_format_json = st.checkbox(
            "Auto-format JSON",
            value=state.auto_format_json,
            help="Automatically format JSON input"
        )


def _render_main_content(state: LocatorTesterState):
    """Render the main content area."""
    # Parameter input section
    _render_parameter_input(state)
    
    # Test execution section
    _render_test_execution(state)
    
    # Results section
    _render_results(state)
    
    # Test history section
    if state.show_test_history:
        _render_test_history(state)


def _render_parameter_input(state: LocatorTesterState):
    """Render the parameter input section."""
    st.header("📝 Function Parameters")
    
    func_info = FUNCTION_DESCRIPTIONS[state.selected_function]
    required_params = func_info["parameters"]
    
    # Create parameter input fields based on function requirements
    if "step_data" in required_params:
        _render_step_data_input(state)
    
    if "element_matches" in required_params:
        _render_element_matches_input(state)
    
    if "step_no" in required_params:
        state.step_no = st.text_input(
            "Step Number",
            value=state.step_no,
            help="Step number for logging context"
        )
    
    if "test_case_id" in required_params:
        state.test_case_id = st.text_input(
            "Test Case ID",
            value=state.test_case_id,
            help="Test case ID for logging context"
        )
    
    if "element" in required_params:
        _render_element_input(state)
    
    if "selector" in required_params:
        state.selector = st.text_input(
            "CSS Selector",
            value=state.selector,
            help="CSS selector to evaluate"
        )
    
    if "xpath" in required_params:
        state.xpath = st.text_input(
            "XPath Expression",
            value=state.xpath,
            help="XPath expression to evaluate"
        )
    
    if "element_match" in required_params:
        _render_element_match_input(state)
    
    if "reason" in required_params:
        state.reason = st.text_input(
            "Reason",
            value=state.reason,
            help="Reason for the resolution"
        )
    
    if "confidence" in required_params:
        state.confidence = st.slider(
            "Confidence Score",
            min_value=0.0,
            max_value=1.0,
            value=state.confidence,
            step=0.01,
            help="Confidence score (0.0 to 1.0)"
        )


def _render_step_data_input(state: LocatorTesterState):
    """Render step data JSON input."""
    st.subheader("Step Data (JSON)")
    
    state.step_data_json = st.text_area(
        "Step Data",
        value=state.step_data_json,
        height=150,
        help="JSON object containing step data with locator_strategy and locator fields",
        key="step_data_input"
    )
    
    # Validate JSON
    if state.step_data_json.strip():
        is_valid, result = validate_json_input(state.step_data_json, dict)
        if not is_valid:
            st.error(f"Invalid JSON: {result}")
            state.update_validation_error("step_data", result)
        else:
            state.update_validation_error("step_data", "")
            if state.auto_format_json:
                formatted = json.dumps(result, indent=2)
                if formatted != state.step_data_json:
                    state.step_data_json = formatted
                    st.rerun()


def _render_element_matches_input(state: LocatorTesterState):
    """Render element matches JSON input."""
    st.subheader("Element Matches (JSON)")
    
    state.element_matches_json = st.text_area(
        "Element Matches",
        value=state.element_matches_json,
        height=200,
        help="JSON array containing element match objects",
        key="element_matches_input"
    )
    
    # Validate JSON
    if state.element_matches_json.strip():
        is_valid, result = validate_json_input(state.element_matches_json, list)
        if not is_valid:
            st.error(f"Invalid JSON: {result}")
            state.update_validation_error("element_matches", result)
        else:
            state.update_validation_error("element_matches", "")
            if state.auto_format_json:
                formatted = json.dumps(result, indent=2)
                if formatted != state.element_matches_json:
                    state.element_matches_json = formatted
                    st.rerun()


def _render_element_input(state: LocatorTesterState):
    """Render element JSON input."""
    st.subheader("Element (JSON)")
    
    state.element_json = st.text_area(
        "Element",
        value=state.element_json,
        height=120,
        help="JSON object containing element data with selector, xpath, and attributes",
        key="element_input"
    )
    
    # Validate JSON
    if state.element_json.strip():
        is_valid, result = validate_json_input(state.element_json, dict)
        if not is_valid:
            st.error(f"Invalid JSON: {result}")
            state.update_validation_error("element", result)
        else:
            state.update_validation_error("element", "")


def _render_element_match_input(state: LocatorTesterState):
    """Render element match JSON input."""
    st.subheader("Element Match (JSON)")
    
    state.element_match_json = st.text_area(
        "Element Match",
        value=state.element_match_json,
        height=150,
        help="JSON object containing element match data",
        key="element_match_input"
    )
    
    # Validate JSON
    if state.element_match_json.strip():
        is_valid, result = validate_json_input(state.element_match_json, dict)
        if not is_valid:
            st.error(f"Invalid JSON: {result}")
            state.update_validation_error("element_match", result)
        else:
            state.update_validation_error("element_match", "")


def _load_sample_data(state: LocatorTesterState, scenario: str):
    """Load sample data for the selected function and scenario."""
    try:
        sample_data = generate_sample_data(state.selected_function, scenario)
        
        if "step_data" in sample_data:
            state.step_data_json = json.dumps(sample_data["step_data"], indent=2)
        
        if "element_matches" in sample_data:
            state.element_matches_json = json.dumps(sample_data["element_matches"], indent=2)
        
        if "step_no" in sample_data:
            state.step_no = sample_data["step_no"]
        
        if "test_case_id" in sample_data:
            state.test_case_id = sample_data["test_case_id"]
        
        if "element" in sample_data:
            state.element_json = json.dumps(sample_data["element"], indent=2)
        
        if "selector" in sample_data:
            state.selector = sample_data["selector"]
        
        if "xpath" in sample_data:
            state.xpath = sample_data["xpath"]
        
        if "element_match" in sample_data:
            state.element_match_json = json.dumps(sample_data["element_match"], indent=2)
        
        if "reason" in sample_data:
            state.reason = sample_data["reason"]
        
        if "confidence" in sample_data:
            state.confidence = sample_data["confidence"]
        
        state._clear_validation_errors()
        
        debug(f"Loaded sample data for {state.selected_function.value} with scenario {scenario}",
              stage="locator_tester", operation="sample_data_load",
              context={"function": state.selected_function.value, "scenario": scenario})
        
    except Exception as e:
        st.error(f"Error loading sample data: {str(e)}")
        debug(f"Error loading sample data: {str(e)}",
              stage="locator_tester", operation="sample_data_load_error",
              context={"function": state.selected_function.value, "scenario": scenario, "error": str(e)})


def _render_test_execution(state: LocatorTesterState):
    """Render the test execution section."""
    st.header("🚀 Test Execution")

    # Check if we can execute the test
    can_execute = not state.has_validation_errors() and not state.execution_in_progress

    if state.has_validation_errors():
        st.warning("⚠️ Please fix validation errors before executing the test.")
        for field, error in state.validation_errors.items():
            st.error(f"{field}: {error}")

    col1, col2, col3 = st.columns([2, 1, 1])

    with col1:
        if st.button(
            "▶️ Execute Test" if not state.execution_in_progress else "⏳ Executing...",
            disabled=not can_execute,
            help="Execute the selected function with current parameters"
        ):
            _execute_test(state)
            st.rerun()

    with col2:
        if st.button("🔄 Reset Parameters", help="Reset all parameters to default values"):
            state._reset_input_parameters()
            state._clear_validation_errors()
            st.rerun()

    with col3:
        if st.button("📋 Validate", help="Validate current parameters without executing"):
            _validate_parameters(state)
            st.rerun()


def _render_results(state: LocatorTesterState):
    """Render the test results section."""
    if state.current_result is None:
        return

    st.header("📊 Test Results")

    result = state.current_result
    formatted_result = format_test_result(result)

    # Status indicator
    if result.success:
        st.markdown(f"""
        <div class="result-success">
            <h4>{formatted_result['status']}</h4>
            <p><strong>Function:</strong> {formatted_result['function']}</p>
            <p><strong>Execution Time:</strong> {formatted_result['execution_time']}</p>
            <p><strong>Timestamp:</strong> {formatted_result['timestamp']}</p>
        </div>
        """, unsafe_allow_html=True)
    else:
        st.markdown(f"""
        <div class="result-error">
            <h4>{formatted_result['status']}</h4>
            <p><strong>Function:</strong> {formatted_result['function']}</p>
            <p><strong>Error Type:</strong> {formatted_result['error']['type']}</p>
            <p><strong>Error Message:</strong> {formatted_result['error']['message']}</p>
            <p><strong>Timestamp:</strong> {formatted_result['timestamp']}</p>
        </div>
        """, unsafe_allow_html=True)

    # Result details
    if result.success:
        st.subheader("📋 Result Details")

        # Result summary
        if 'result_summary' in formatted_result:
            st.info(f"**Summary:** {formatted_result['result_summary']}")

        # Full result display
        with st.expander("🔍 Full Result", expanded=False):
            if isinstance(result.result, dict):
                st.json(result.result)
            elif isinstance(result.result, (list, tuple)):
                st.json(list(result.result))
            else:
                st.code(str(result.result))

        # Result type information
        st.caption(f"Result Type: {formatted_result.get('result_type', 'Unknown')}")


def _render_test_history(state: LocatorTesterState):
    """Render the test history section."""
    if not state.test_history:
        return

    st.header("📜 Test History")

    # Summary statistics
    total_tests = len(state.test_history)
    successful_tests = sum(1 for result in state.test_history if result.success)
    failed_tests = total_tests - successful_tests

    col1, col2, col3 = st.columns(3)
    with col1:
        st.metric("Total Tests", total_tests)
    with col2:
        st.metric("Successful", successful_tests, delta=f"{successful_tests/total_tests*100:.1f}%")
    with col3:
        st.metric("Failed", failed_tests, delta=f"{failed_tests/total_tests*100:.1f}%")

    # History table
    st.subheader("Test Execution History")

    history_data = []
    for i, result in enumerate(reversed(state.test_history[-10:])):  # Show last 10 tests
        formatted = format_test_result(result)
        history_data.append({
            "Test #": total_tests - i,
            "Status": "✅" if result.success else "❌",
            "Function": result.function_name,
            "Execution Time": formatted['execution_time'],
            "Timestamp": formatted['timestamp']
        })

    if history_data:
        st.dataframe(history_data, use_container_width=True)


def _execute_test(state: LocatorTesterState):
    """Execute the selected test function."""
    try:
        state.set_execution_in_progress(True)

        debug(f"Executing test for function {state.selected_function.value}",
              stage="locator_tester", operation="test_execution",
              context={"function": state.selected_function.value})

        # Prepare parameters
        parameters = _prepare_function_parameters(state)

        # Validate parameters
        is_valid, error_msg = validate_function_parameters(state.selected_function, parameters)
        if not is_valid:
            result = TestResult(
                success=False,
                result=None,
                execution_time=0.0,
                error_message=error_msg,
                error_type="ParameterValidationError",
                function_name=state.selected_function.value
            )
            state.update_test_result(result)
            return

        # Execute function
        start_time = time.time()

        try:
            function_result = _call_function(state.selected_function, parameters)
            execution_time = time.time() - start_time

            result = TestResult(
                success=True,
                result=function_result,
                execution_time=execution_time,
                function_name=state.selected_function.value
            )

        except Exception as e:
            execution_time = time.time() - start_time
            result = TestResult(
                success=False,
                result=None,
                execution_time=execution_time,
                error_message=str(e),
                error_type=type(e).__name__,
                function_name=state.selected_function.value
            )

        state.update_test_result(result)

        debug(f"Test execution completed for {state.selected_function.value}",
              stage="locator_tester", operation="test_execution_complete",
              context={"function": state.selected_function.value, "success": result.success,
                      "execution_time": result.execution_time})

    except Exception as e:
        # Handle unexpected errors
        result = TestResult(
            success=False,
            result=None,
            execution_time=0.0,
            error_message=f"Unexpected error: {str(e)}",
            error_type="UnexpectedError",
            function_name=state.selected_function.value
        )
        state.update_test_result(result)

        debug(f"Unexpected error during test execution: {str(e)}",
              stage="locator_tester", operation="test_execution_error",
              context={"function": state.selected_function.value, "error": str(e),
                      "traceback": traceback.format_exc()})


def _prepare_function_parameters(state: LocatorTesterState) -> Dict[str, Any]:
    """Prepare parameters for function execution."""
    parameters = {}

    func_info = FUNCTION_DESCRIPTIONS[state.selected_function]
    required_params = func_info["parameters"]

    for param in required_params:
        if param == "step_data":
            if state.step_data_json.strip():
                is_valid, result = validate_json_input(state.step_data_json, dict)
                if is_valid:
                    parameters[param] = result

        elif param == "element_matches":
            if state.element_matches_json.strip():
                is_valid, result = validate_json_input(state.element_matches_json, list)
                if is_valid:
                    parameters[param] = result

        elif param == "element":
            if state.element_json.strip():
                is_valid, result = validate_json_input(state.element_json, dict)
                if is_valid:
                    parameters[param] = result

        elif param == "element_match":
            if state.element_match_json.strip():
                is_valid, result = validate_json_input(state.element_match_json, dict)
                if is_valid:
                    parameters[param] = result

        elif param == "step_no":
            parameters[param] = state.step_no

        elif param == "test_case_id":
            parameters[param] = state.test_case_id

        elif param == "selector":
            parameters[param] = state.selector

        elif param == "xpath":
            parameters[param] = state.xpath

        elif param == "reason":
            parameters[param] = state.reason

        elif param == "confidence":
            parameters[param] = state.confidence

    return parameters


def _call_function(function: TestableFunction, parameters: Dict[str, Any]) -> Any:
    """Call the specified function with the given parameters."""
    if function == TestableFunction.RESOLVE_CONFLICTS:
        return resolve_locator_conflicts(**parameters)
    elif function == TestableFunction.DETECT_CONFLICTS:
        return _detect_locator_conflicts(**parameters)
    elif function == TestableFunction.APPLY_HEURISTICS:
        return _apply_resolution_heuristics(**parameters)
    elif function == TestableFunction.CALCULATE_RELIABILITY:
        return _calculate_locator_reliability(**parameters)
    elif function == TestableFunction.EVALUATE_CSS:
        return _evaluate_css_selector_reliability(**parameters)
    elif function == TestableFunction.EVALUATE_XPATH:
        return _evaluate_xpath_reliability(**parameters)
    elif function == TestableFunction.CREATE_RESULT:
        return _create_resolution_result(**parameters)
    else:
        raise ValueError(f"Unknown function: {function.value}")


def _validate_parameters(state: LocatorTesterState):
    """Validate current parameters without executing the test."""
    try:
        parameters = _prepare_function_parameters(state)
        is_valid, error_msg = validate_function_parameters(state.selected_function, parameters)

        if is_valid:
            st.success("✅ All parameters are valid!")
        else:
            st.error(f"❌ Parameter validation failed: {error_msg}")

        debug(f"Parameter validation completed for {state.selected_function.value}",
              stage="locator_tester", operation="parameter_validation",
              context={"function": state.selected_function.value, "valid": is_valid,
                      "error": error_msg if not is_valid else None})

    except Exception as e:
        st.error(f"❌ Validation error: {str(e)}")
        debug(f"Parameter validation error: {str(e)}",
              stage="locator_tester", operation="parameter_validation_error",
              context={"function": state.selected_function.value, "error": str(e)})


if __name__ == "__main__":
    main()
