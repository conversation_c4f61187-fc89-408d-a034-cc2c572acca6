"""
Constants and configuration for the Locator Resolver Testing Application.

This module contains all constants, sample data, and configuration settings
used by the locator resolver testing interface.

© 2025 Cogniron All Rights Reserved.
"""

from typing import Dict, List, Any
from enum import Enum

# Application Configuration
APP_TITLE = "🔍 Locator Resolver Testing Interface"
APP_DESCRIPTION = """
Interactive testing interface for locator_resolver module functions.
Validate behavior, troubleshoot issues, and test different scenarios.
"""

# Available Functions for Testing
class TestableFunction(Enum):
    """Enumeration of testable locator_resolver functions."""
    RESOLVE_CONFLICTS = "resolve_locator_conflicts"
    DETECT_CONFLICTS = "_detect_locator_conflicts"
    APPLY_HEURISTICS = "_apply_resolution_heuristics"
    CALCULATE_RELIABILITY = "_calculate_locator_reliability"
    EVALUATE_CSS = "_evaluate_css_selector_reliability"
    EVALUATE_XPATH = "_evaluate_xpath_reliability"
    CREATE_RESULT = "_create_resolution_result"

# Function Descriptions
FUNCTION_DESCRIPTIONS = {
    TestableFunction.RESOLVE_CONFLICTS: {
        "name": "resolve_locator_conflicts",
        "description": "Main function to resolve conflicts between step data locators and element matches",
        "parameters": ["step_data", "element_matches", "step_no", "test_case_id"],
        "returns": "Dict with resolved locator information"
    },
    TestableFunction.DETECT_CONFLICTS: {
        "name": "_detect_locator_conflicts",
        "description": "Detect if there are conflicts between step data and element matches",
        "parameters": ["step_data", "element_matches"],
        "returns": "Boolean indicating if conflicts exist"
    },
    TestableFunction.APPLY_HEURISTICS: {
        "name": "_apply_resolution_heuristics",
        "description": "Apply heuristic rules to resolve locator conflicts",
        "parameters": ["step_data", "element_matches", "step_no", "test_case_id"],
        "returns": "Dict with resolved locator information"
    },
    TestableFunction.CALCULATE_RELIABILITY: {
        "name": "_calculate_locator_reliability",
        "description": "Calculate reliability score for an element's locator strategies",
        "parameters": ["element"],
        "returns": "Integer reliability score"
    },
    TestableFunction.EVALUATE_CSS: {
        "name": "_evaluate_css_selector_reliability",
        "description": "Evaluate the reliability of a CSS selector",
        "parameters": ["selector"],
        "returns": "Integer reliability score"
    },
    TestableFunction.EVALUATE_XPATH: {
        "name": "_evaluate_xpath_reliability",
        "description": "Evaluate the reliability of an XPath selector",
        "parameters": ["xpath"],
        "returns": "Integer reliability score"
    },
    TestableFunction.CREATE_RESULT: {
        "name": "_create_resolution_result",
        "description": "Create a standardized resolution result from an element match",
        "parameters": ["element_match", "reason", "confidence"],
        "returns": "Dict with resolved locator information"
    }
}

# Sample Test Data
SAMPLE_STEP_DATA = {
    "basic_css": {
        "locator_strategy": "css",
        "locator": "input[name='username']",
        "action": "type",
        "step_type": "input"
    },
    "basic_xpath": {
        "locator_strategy": "xpath",
        "locator": "//input[@name='username']",
        "action": "type",
        "step_type": "input"
    },
    "id_locator": {
        "locator_strategy": "id",
        "locator": "login-button",
        "action": "click",
        "step_type": "button"
    },
    "navigation": {
        "locator_strategy": "url",
        "locator": "https://example.com/login",
        "action": "navigate",
        "step_type": "navigation"
    },
    "empty_locator": {
        "locator_strategy": "",
        "locator": "",
        "action": "click",
        "step_type": "button"
    }
}

SAMPLE_ELEMENT_MATCHES = {
    "single_match": [
        {
            "element": {
                "selector": "input[name='username']",
                "xpath": "//input[@name='username']",
                "attributes": {"name": "username", "type": "text", "id": "user-input"}
            },
            "score": 0.95,
            "manually_selected": False,
            "locator_strategy": "css",
            "locator": "input[name='username']"
        }
    ],
    "multiple_matches": [
        {
            "element": {
                "selector": "#login-btn",
                "xpath": "//button[@id='login-btn']",
                "attributes": {"id": "login-btn", "type": "button", "class": "btn primary"}
            },
            "score": 0.98,
            "manually_selected": True,
            "locator_strategy": "id",
            "locator": "login-btn"
        },
        {
            "element": {
                "selector": ".btn.primary",
                "xpath": "//button[@class='btn primary']",
                "attributes": {"class": "btn primary", "type": "button"}
            },
            "score": 0.75,
            "manually_selected": False,
            "locator_strategy": "css",
            "locator": ".btn.primary"
        },
        {
            "element": {
                "selector": "button[type='button']",
                "xpath": "//button[@type='button'][1]",
                "attributes": {"type": "button"}
            },
            "score": 0.60,
            "manually_selected": False,
            "locator_strategy": "css",
            "locator": "button[type='button']"
        }
    ],
    "conflicting_matches": [
        {
            "element": {
                "selector": "input[name='email']",
                "xpath": "//input[@name='email']",
                "attributes": {"name": "email", "type": "email"}
            },
            "score": 0.85,
            "manually_selected": False,
            "locator_strategy": "css",
            "locator": "input[name='email']"
        },
        {
            "element": {
                "selector": "#email-field",
                "xpath": "//input[@id='email-field']",
                "attributes": {"id": "email-field", "name": "email", "type": "email"}
            },
            "score": 0.90,
            "manually_selected": False,
            "locator_strategy": "id",
            "locator": "email-field"
        }
    ],
    "empty_matches": []
}

# Sample CSS Selectors for Testing
SAMPLE_CSS_SELECTORS = [
    "#unique-id",
    ".class-name",
    "input[name='username']",
    "button[type='submit']",
    "[data-testid='login-button']",
    "[data-test='submit-form']",
    "div > p.content",
    "ul li:first-child",
    ".container .form input",
    "button",
    ""
]

# Sample XPath Expressions for Testing
SAMPLE_XPATH_EXPRESSIONS = [
    "//input[@id='username']",
    "//button[@name='submit']",
    "//div[@class='container']//input",
    "//input[@data-testid='email-field']",
    "//button[text()='Login']",
    "//div[contains(@class, 'form')]",
    "//input[@type='password'][1]",
    "//a[contains(@href, 'login')]",
    "//span[normalize-space()='Submit']",
    "//input",
    ""
]

# Sample Element Data for Testing
SAMPLE_ELEMENTS = [
    {
        "selector": "#login-button",
        "xpath": "//button[@id='login-button']",
        "attributes": {"id": "login-button", "type": "button", "class": "btn primary"}
    },
    {
        "selector": "input[name='username']",
        "xpath": "//input[@name='username']",
        "attributes": {"name": "username", "type": "text", "placeholder": "Enter username"}
    },
    {
        "selector": "[data-testid='submit-form']",
        "xpath": "//button[@data-testid='submit-form']",
        "attributes": {"data-testid": "submit-form", "type": "submit"}
    },
    {
        "selector": ".form-control",
        "xpath": "//input[@class='form-control']",
        "attributes": {"class": "form-control", "type": "text"}
    },
    {
        "selector": "",
        "xpath": "",
        "attributes": {}
    }
]

# UI Configuration
UI_CONFIG = {
    "sidebar_width": 300,
    "main_content_padding": "1rem",
    "section_spacing": "2rem",
    "success_color": "#28a745",
    "warning_color": "#ffc107",
    "error_color": "#dc3545",
    "info_color": "#17a2b8"
}

# Test Scenarios
TEST_SCENARIOS = {
    "basic_resolution": {
        "name": "Basic Conflict Resolution",
        "description": "Test basic conflict resolution between step data and element matches",
        "step_data": SAMPLE_STEP_DATA["basic_css"],
        "element_matches": SAMPLE_ELEMENT_MATCHES["single_match"],
        "step_no": "1",
        "test_case_id": "TC_001"
    },
    "manual_selection": {
        "name": "Manual Selection Priority",
        "description": "Test that manually selected elements get highest priority",
        "step_data": SAMPLE_STEP_DATA["id_locator"],
        "element_matches": SAMPLE_ELEMENT_MATCHES["multiple_matches"],
        "step_no": "2",
        "test_case_id": "TC_002"
    },
    "conflicting_strategies": {
        "name": "Conflicting Strategies",
        "description": "Test resolution when step data conflicts with element matches",
        "step_data": SAMPLE_STEP_DATA["basic_xpath"],
        "element_matches": SAMPLE_ELEMENT_MATCHES["conflicting_matches"],
        "step_no": "3",
        "test_case_id": "TC_003"
    },
    "navigation_step": {
        "name": "Navigation Step",
        "description": "Test handling of navigation steps with URL locators",
        "step_data": SAMPLE_STEP_DATA["navigation"],
        "element_matches": SAMPLE_ELEMENT_MATCHES["empty_matches"],
        "step_no": "1",
        "test_case_id": "TC_004"
    },
    "empty_data": {
        "name": "Empty Data Handling",
        "description": "Test handling of empty or invalid data",
        "step_data": SAMPLE_STEP_DATA["empty_locator"],
        "element_matches": SAMPLE_ELEMENT_MATCHES["empty_matches"],
        "step_no": "1",
        "test_case_id": "TC_005"
    }
}
